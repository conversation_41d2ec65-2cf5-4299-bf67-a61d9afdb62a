
<div align="center">
<h1 align="center" style="font-size: 2cm;"> NarratoAI 😎📽️ </h1>
<h3 align="center">一站式 AI 影视解说+自动化剪辑工具🎬🎞️ </h3>


<h3>📖 <a href="README-en.md">English</a> | 简体中文 | <a href="README-ja.md">日本語</a> </h3>
<div align="center">

[//]: # (  <a href="https://trendshift.io/repositories/8731" target="_blank"><img src="https://trendshift.io/api/badge/repositories/8731" alt="harry0703%2FNarratoAI | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>)
</div>
<br>
NarratoAI 是一个自动化影视解说工具，基于LLM实现文案撰写、自动化视频剪辑、配音和字幕生成的一站式流程，助力高效内容创作。
<br>

[![madewithlove](https://img.shields.io/badge/made_with-%E2%9D%A4-red?style=for-the-badge&labelColor=orange)](https://github.com/linyqh/NarratoAI)
[![GitHub license](https://img.shields.io/github/license/linyqh/NarratoAI?style=for-the-badge)](https://github.com/linyqh/NarratoAI/blob/main/LICENSE)
[![GitHub issues](https://img.shields.io/github/issues/linyqh/NarratoAI?style=for-the-badge)](https://github.com/linyqh/NarratoAI/issues)
[![GitHub stars](https://img.shields.io/github/stars/linyqh/NarratoAI?style=for-the-badge)](https://github.com/linyqh/NarratoAI/stargazers)

<a href="https://discord.com/invite/V2pbAqqQNb" target="_blank">💬 加入 discord 开源社区，获取项目动态和最新资讯。</a>

<h2><a href="https://p9mf6rjv3c.feishu.cn/wiki/SP8swLLZki5WRWkhuFvc2CyInDg?from=from_copylink" target="_blank">🎉🎉🎉 官方文档 🎉🎉🎉</a> </h2>
<h3>首页</h3>

![](docs/index-zh.png)

<h3>视频审查界面</h3>

![](docs/check-zh.png)

</div>

## 最新资讯
- 2025.05.11 发布新版本 0.6.0，支持 **短剧解说** 和 优化剪辑流程
- 2025.03.06 发布新版本 0.5.2，支持 DeepSeek R1 和 DeepSeek V3 模型进行短剧混剪
- 2024.12.16 发布新版本 0.3.9，支持阿里 Qwen2-VL 模型理解视频；支持短剧混剪
- 2024.11.24 开通 discord 社群：https://discord.com/invite/V2pbAqqQNb
- 2024.11.11 迁移开源社群，欢迎加入！ [加入官方社群](https://github.com/linyqh/NarratoAI/wiki)
- 2024.11.10 发布官方文档，详情参见 [官方文档](https://p9mf6rjv3c.feishu.cn/wiki/SP8swLLZki5WRWkhuFvc2CyInDg)
- 2024.11.10 发布新版本 v0.3.5；优化视频剪辑流程，

## 重磅福利 🎉
即日起全面支持DeepSeek模型！注册即享2000万免费Token（价值14元平台配额），剪辑10分钟视频仅需0.1元！  

🔥 快速领福利：  
1️⃣ 点击链接注册：https://cloud.siliconflow.cn/i/pyOKqFCV  
2️⃣ 使用手机号登录，**务必填写邀请码：pyOKqFCV**  
3️⃣ 领取14元配额，极速体验高性价比AI剪辑  

💡 小成本大创作：  
硅基流动API Key一键接入，智能剪辑效率翻倍！  
（注：邀请码为福利领取唯一凭证，注册后自动到账）  

立即行动，用「pyOKqFCV」解锁你的AI生产力！

😊 更新步骤：
整合包：点击 update.bat 一键更新脚本
代码构建：使用 git pull 拉去最新代码

## 公告 📢
_**注意⚠️：近期在 x (推特) 上发现有人冒充作者在 pump.fun 平台上发行代币！ 这是骗子！！！ 不要被割了韭菜
！！！目前 NarratoAI 没有在 x(推特) 上做任何官方宣传，注意甄别**_

下面是此人 x(推特) 首页截图

<img src="https://github.com/user-attachments/assets/c492ab99-52cd-4ba2-8695-1bd2073ecf12" alt="Screenshot_20250109_114131_Samsung Internet" style="width:30%; height:auto;">

## 未来计划 🥳
- [x] windows 整合包发布
- [x] 优化剧情生成流程，提升生成效果
- [x] 发布 0.3.5 整合包
- [x] 支持阿里 Qwen2-VL 大模型理解视频
- [x] 支持短剧混剪
  - [x] 一键合并素材
  - [x] 一键转录
  - [x] 一键清理缓存
- [ ] 支持导出剪映草稿
- [X] 支持短剧解说
- [ ] 主角人脸匹配
- [ ] 支持根据口播，文案，视频素材自动匹配
- [ ] 支持更多 TTS 引擎
- [ ] ...

## 配置要求 📦

- 建议最低 CPU 4核或以上，内存 8G 或以上，显卡非必须
- Windows 10/11 或 MacOS 11.0 以上系统
- [Python 3.12+](https://www.python.org/downloads/)

## 反馈建议 📢

👏 1. 可以提交 [issue](https://github.com/linyqh/NarratoAI/issues)或者 [pull request](https://github.com/linyqh/NarratoAI/pulls)

💬 2. [加入开源社区交流群](https://github.com/linyqh/NarratoAI/wiki)

📷 3. 关注公众号【NarratoAI助手】，掌握最新资讯

## 参考项目 📚
- https://github.com/FujiwaraChoki/MoneyPrinter
- https://github.com/harry0703/MoneyPrinterTurbo

该项目基于以上项目重构而来，增加了影视解说功能，感谢大佬的开源精神 🥳🥳🥳 

## 请作者喝一杯咖啡 ☕️
<div style="display: flex; justify-content: space-between;">
  <img src="https://github.com/user-attachments/assets/5038ccfb-addf-4db1-9966-99415989fd0c" alt="Image 1" style="width: 350px; height: 350px; margin: auto;"/>
  <img src="https://github.com/user-attachments/assets/07d4fd58-02f0-425c-8b59-2ab94b4f09f8" alt="Image 2" style="width: 350px; height: 350px; margin: auto;"/>
</div>

## 赞助
[![Powered by DartNode](https://dartnode.com/branding/DN-Open-Source-sm.png)](https://dartnode.com "Powered by DartNode - Free VPS for Open Source")

## 许可证 📝

点击查看 [`LICENSE`](LICENSE) 文件

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=linyqh/NarratoAI&type=Date)](https://star-history.com/#linyqh/NarratoAI&Date)

