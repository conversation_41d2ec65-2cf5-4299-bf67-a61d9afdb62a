# 短剧解说原声片段集成指南

## 📋 更新概述

本次更新为短剧解说脚本生成提示词添加了详细的原声片段使用规范，确保生成的解说脚本能够在适当位置插入原声片段，增强观众的代入感和情感体验。

## 🎬 原声片段使用规范

### 📢 格式要求

原声片段必须严格按照以下JSON格式：
```json
{
  "_id": 序号,
  "timestamp": "开始时间-结束时间",
  "picture": "画面内容描述",
  "narration": "播放原片+序号",
  "OST": 1
}
```

### 🎯 插入策略

#### 1. 🔥 关键情绪爆发点
在角色强烈情绪表达时必须保留原声：
- **愤怒爆发**：角色愤怒咆哮、情绪失控的瞬间
- **感动落泪**：角色感动哭泣、情感宣泄的时刻
- **震惊反应**：角色震惊、不敢置信的表情和台词
- **绝望崩溃**：角色绝望、崩溃的情感表达
- **狂欢庆祝**：角色兴奋、狂欢的情绪高潮

#### 2. 💬 重要对白时刻
保留推动剧情发展的关键台词和对话：
- **身份揭露**：揭示角色真实身份的重要台词
- **真相大白**：揭晓谜底、真相的关键对话
- **情感告白**：爱情告白、情感表达的重要台词
- **威胁警告**：反派威胁、警告的重要对白
- **决定宣布**：角色做出重要决定的宣告

#### 3. 💥 爽点瞬间
在"爽点"时刻保留原声增强痛快感：
- **主角逆袭**：弱者反击、逆转局面的台词
- **反派被打脸**：恶人得到报应、被揭穿的瞬间
- **智商碾压**：主角展现智慧、碾压对手的台词
- **正义伸张**：正义得到伸张、恶有恶报的时刻
- **实力展现**：主角展现真实实力、震撼全场

#### 4. 🎪 悬念节点
在制造悬念或揭晓答案的关键时刻保留原声：
- **悬念制造**：制造悬念、留下疑问的台词
- **答案揭晓**：揭晓答案、解开谜团的对话
- **转折预告**：暗示即将发生转折的重要台词
- **危机降临**：危机来临、紧张时刻的对白

## ⚙️ 技术规范

### 🔧 格式规范
- **OST字段**：设置为1表示保留原声（解说片段设置为0）
- **narration格式**：严格使用"播放原片+序号"（如"播放原片26"）
- **picture字段**：详细描述画面内容，便于后期剪辑参考
- **时间戳精度**：必须与字幕中的重要对白时间精确匹配

### 📊 比例控制
- **原声与解说比例**：3:7（原声30%，解说70%）
- **分布均匀**：原声片段要在整个视频中均匀分布
- **长度适中**：单个原声片段时长控制在3-8秒
- **衔接自然**：原声片段与解说片段之间衔接自然流畅

### 🎯 选择原则
- **情感优先**：优先选择情感强烈的台词和对话
- **剧情关键**：必须是推动剧情发展的重要内容
- **观众共鸣**：选择能引起观众共鸣的经典台词
- **视听效果**：考虑台词的声音效果和表演张力

## 📝 输出示例

```json
{
  "items": [
    {
        "_id": 1,
        "timestamp": "00:00:01,000-00:00:05,500",
        "picture": "女主角林小雨慌张地道歉，男主角沈墨轩冷漠地看着她",
        "narration": "一个普通女孩的命运即将因为一杯咖啡彻底改变！她撞到的这个男人，竟然是...",
        "OST": 0
    },
    {
        "_id": 2,
        "timestamp": "00:00:05,500-00:00:08,000",
        "picture": "沈墨轩质问林小雨，语气冷厉威严",
        "narration": "播放原片2",
        "OST": 1
    },
    {
        "_id": 3,
        "timestamp": "00:00:08,000-00:00:12,000",
        "picture": "林小雨惊慌失措，沈墨轩眼中闪过一丝兴趣",
        "narration": "霸道总裁的经典开场！一杯咖啡引发的爱情故事就这样开始了...",
        "OST": 0
    }
  ]
}
```

## 🔄 使用方法

使用方法与之前完全一致，无需修改调用代码：

```python
from app.services.prompts import PromptManager

prompt = PromptManager.get_prompt(
    category="short_drama_narration",
    name="script_generation",
    parameters={
        "drama_name": "短剧名称",
        "plot_analysis": "剧情分析内容",
        "subtitle_content": "原始字幕内容"
    }
)
```

## 📈 预期效果

通过添加原声片段使用规范，预期能够：
- **增强情感体验**：在关键情绪点保留原声，让观众更有代入感
- **提升观看质量**：重要对白的原声保留，避免信息丢失
- **强化爽点效果**：在爽点时刻保留原声，增强观众的痛快感
- **优化节奏控制**：合理的原声与解说比例，保持观看节奏
- **提高专业水准**：规范的原声片段使用，体现专业制作水平

## ✅ 验证结果

通过测试验证，更新后的提示词：
- ✅ 包含完整的原声片段使用规范
- ✅ 提供详细的插入策略指导
- ✅ 明确技术规范和格式要求
- ✅ 给出具体的输出示例
- ✅ 保持代码完全兼容性

## 🎉 总结

本次更新成功为短剧解说脚本生成提示词添加了专业的原声片段使用规范，为AI生成更高质量、更具观赏性的短剧解说脚本提供了强有力的技术支持。
