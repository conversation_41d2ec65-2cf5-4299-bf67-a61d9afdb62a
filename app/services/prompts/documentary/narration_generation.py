#!/usr/bin/env python
# -*- coding: UTF-8 -*-

"""
@Project: NarratoAI
@File   : narration_generation.py
<AUTHOR> viccy同学
@Date   : 2025/1/7
@Description: 纪录片解说文案生成提示词
"""

from ..base import TextPrompt, PromptMetadata, ModelType, OutputFormat


class NarrationGenerationPrompt(TextPrompt):
    """纪录片解说文案生成提示词"""
    
    def __init__(self):
        metadata = PromptMetadata(
            name="narration_generation",
            category="documentary",
            version="v1.0",
            description="根据视频帧分析结果生成纪录片解说文案，特别适用于荒野建造类内容",
            model_type=ModelType.TEXT,
            output_format=OutputFormat.JSON,
            tags=["纪录片", "解说文案", "荒野建造", "文案生成"],
            parameters=["video_frame_description"]
        )
        super().__init__(metadata)
        
        self._system_prompt = "你是一名专业的短视频解说文案撰写专家，擅长创作引人入胜的纪录片解说内容。"
        
    def get_template(self) -> str:
        return """我是一名荒野建造解说的博主，以下是一些同行的对标文案，请你深度学习并总结这些文案的风格特点跟内容特点：

<example_text_1>
解压助眠的天花板就是荒野建造，沉浸丝滑的搭建过程可以说每一帧都是极致享受，我保证强迫症来了都找不出一丁点毛病。更别说全屋严丝合缝的拼接工艺，还能轻松抵御零下二十度气温，让你居住的每一天都温暖如春。
在家闲不住的西姆今天也打算来一次野外建造，行走没多久他就发现许多倒塌的树，任由它们自生自灭不如将其利用起来。想到这他就开始挥舞铲子要把地基挖掘出来，虽然每次只能挖一点点，但架不住他体能惊人。没多长时间一个 2x3 的深坑就赫然出现，这深度住他一人绰绰有余。
随后他去附近收集来原木，这些都是搭建墙壁的最好材料。而在投入使用前自然要把表皮刮掉，防止森林中的白蚁蛀虫。处理好一大堆后西姆还在两端打孔，使用木钉固定在一起。这可不是用来做墙壁的，而是做庇护所的承重柱。只要木头间的缝隙足够紧密，那搭建出的木屋就能足够坚固。
每向上搭建一层，他都会在中间塞入苔藓防寒，保证不会泄露一丝热量。其他几面也是用相同方法，很快西姆就做好了三面墙壁，每一根木头都极其工整，保证强迫症来了都要点个赞再走。
在继续搭建墙壁前西姆决定将壁炉制作出来，毕竟森林夜晚的气温会很低，保暖措施可是重中之重。完成后他找来一块大树皮用来充当庇护所的大门，而上面刮掉的木屑还能作为壁炉的引火物，可以说再完美不过。
测试了排烟没问题后他才开始搭建最后一面墙壁，这一面要预留门和窗，所以在搭建到一半后还需要在原木中间开出卡口，让自己劈砍时能轻松许多。此时只需将另外一根如法炮制，两端拼接在一起后就是一扇大小适中的窗户。而随着随后一层苔藓铺好，最后一根原木落位，这个庇护所的雏形就算完成。
</example_text_1>

<example_text_2>
解压助眠的天花板就是荒野建造，沉浸丝滑的搭建过程每一帧都是极致享受，全屋严丝合缝的拼接工艺，能轻松抵御零下二十度气温，居住体验温暖如春。
在家闲不住的西姆开启野外建造。他发现倒塌的树，决定加以利用。先挖掘出 2x3 的深坑作为地基，接着收集原木，刮掉表皮防白蚁蛀虫，打孔用木钉固定制作承重柱。搭建墙壁时，每一层都塞入苔藓防寒，很快做好三面墙。
为应对森林夜晚低温，西姆制作壁炉，用大树皮当大门，刮下的木屑做引火物。搭建最后一面墙时预留门窗，通过在原木中间开口拼接做出窗户。大门采用榫卯结构安装，严丝合缝。
搭建屋顶时，先固定外围原木，再平铺原木形成斜面屋顶，之后用苔藓、黏土密封缝隙，铺上枯叶和泥土。为美观，在木屋覆盖苔藓，移植小树点缀。完工时遇大雨，木屋防水良好。
西姆利用墙壁凹槽镶嵌床框，铺上苔藓、床单枕头做成床。劳作一天后，他用壁炉烤牛肉享用。建造一星期后，他开始野外露营。
后来西姆回家补给物资，回来时森林大雪纷飞。他劈柴储备，带回食物、调味料和被褥，提高居住舒适度，还用干草做靠垫。他用壁炉烤牛排，搭配红酒。
第二天，积雪融化，西姆制作室外篝火堆防野兽。用大树夹缝掰弯木棍堆积而成，晚上点燃处理废料，结束后用雪球灭火，最后在室内二十五度的环境中裹被入睡。
</example_text_2>

<video_frame_description>
${video_frame_description}
</video_frame_description>

我正在尝试做这个内容的解说纪录片视频，我需要你以 <video_frame_description> </video_frame_description> 中的内容为解说目标，根据我刚才提供给你的对标文案特点，以及你总结的特点，帮我生成一段关于荒野建造的解说文案，文案需要符合平台受欢迎的解说风格，请使用 json 格式进行输出；使用 <output> 中的输出格式：

<output>
{
  "items": [
    {
        "_id": 1,
        "timestamp": "00:00:05,390-00:00:10,430",
        "picture": "画面描述",
        "narration": "解说文案"
    }
  ]
}
</output>

<restriction>
1. 只输出 json 内容，不要输出其他任何说明性的文字
2. 解说文案的语言使用 简体中文
3. 严禁虚构画面，所有画面只能从 <video_frame_description> 中摘取
4. 严禁虚构时间戳，所有时间戳只能从 <video_frame_description> 中摘取
5. 解说文案要生动有趣，符合荒野建造解说的风格特点
6. 每个片段的解说文案要与画面内容高度匹配
7. 保持解说的连贯性和故事性
</restriction>"""
